package com.ruoyi.system.domain;

import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 会议对象 conference
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public class Conference extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    private Long id;

    /**
     * 会议名称
     */
    @Excel(name = "会议名称")
    private String conferenceTitle;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 会议地址
     */
    @Excel(name = "会议地址")
    private String address;

    /**
     * 酒店房间开放预定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "酒店房间开放预定时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bookingOpenTime;

    /**
     * 酒店房间关闭预定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "酒店房间关闭预定时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bookingCloseTime;

    /**
     * 参会人员数量
     */
    @Excel(name = "参会人员数量")
    private Integer participantCount;

    /**
     * 酒店名称
     */
    @Excel(name = "酒店名称")
    private String hotelName;

    /**
     * 酒店地址
     */
    @Excel(name = "酒店地址")
    private String hotelAddress;

    /**
     * 房间预定区间起始
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "房间预定区间起始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bookingRangeStart;

    /**
     * 房间预定时间终止
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "房间预定时间终止", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bookingRangeEnd;

    /**
     * 是否启用,启用状态下小程序可以见
     */
    @Excel(name = "是否启用", readConverterExp = "Y=启用,N=禁用")
    private String enable;

    /**
     * 操作人
     */
    @Excel(name = "操作人")
    private String operator;

    /**
     * 会议照片路径
     */
    @Excel(name = "会议照片路径")
    private String photoPath;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setConferenceTitle(String conferenceTitle) {
        this.conferenceTitle = conferenceTitle;
    }

    public String getConferenceTitle() {
        return conferenceTitle;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setBookingOpenTime(Date bookingOpenTime) {
        this.bookingOpenTime = bookingOpenTime;
    }

    public Date getBookingOpenTime() {
        return bookingOpenTime;
    }

    public void setBookingCloseTime(Date bookingCloseTime) {
        this.bookingCloseTime = bookingCloseTime;
    }

    public Date getBookingCloseTime() {
        return bookingCloseTime;
    }

    public void setParticipantCount(Integer participantCount) {
        this.participantCount = participantCount;
    }

    public Integer getParticipantCount() {
        return participantCount;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelAddress(String hotelAddress) {
        this.hotelAddress = hotelAddress;
    }

    public String getHotelAddress() {
        return hotelAddress;
    }

    public void setBookingRangeStart(Date bookingRangeStart) {
        this.bookingRangeStart = bookingRangeStart;
    }

    public Date getBookingRangeStart() {
        return bookingRangeStart;
    }

    public void setBookingRangeEnd(Date bookingRangeEnd) {
        this.bookingRangeEnd = bookingRangeEnd;
    }

    public Date getBookingRangeEnd() {
        return bookingRangeEnd;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getEnable() {
        return enable;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public boolean enable() {
        return Objects.equals(enable, "Y");
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("conferenceTitle", getConferenceTitle())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("address", getAddress())
                .append("bookingOpenTime", getBookingOpenTime())
                .append("bookingCloseTime", getBookingCloseTime())
                .append("participantCount", getParticipantCount())
                .append("hotelName", getHotelName())
                .append("hotelAddress", getHotelAddress())
                .append("bookingRangeStart", getBookingRangeStart())
                .append("bookingRangeEnd", getBookingRangeEnd())
                .append("enable", getEnable())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("operator", getOperator())
                .toString();
    }
}
