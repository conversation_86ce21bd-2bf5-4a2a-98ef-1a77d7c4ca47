package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 识别码对象 category_code
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class CategoryCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 识别码 */
    @Excel(name = "识别码")
    private String categoryId;

    /** 会议ID */
    @Excel(name = "会议ID")
    private Long conference;

    /** 房间ID */
    @Excel(name = "房间ID")
    private Long roomId;

    /** 房间数量 */
    @Excel(name = "房间数量")
    private Integer roomCount;

    /** 会议名称（关联查询用） */
    private String conferenceTitle;

    /** 房间名称（关联查询用） */
    private String roomTitle;

    public void setCategoryId(String categoryId) 
    {
        this.categoryId = categoryId;
    }

    public String getCategoryId() 
    {
        return categoryId;
    }
    public void setConference(Long conference) 
    {
        this.conference = conference;
    }

    public Long getConference() 
    {
        return conference;
    }
    public void setRoomId(Long roomId) 
    {
        this.roomId = roomId;
    }

    public Long getRoomId() 
    {
        return roomId;
    }
    public void setRoomCount(Integer roomCount) 
    {
        this.roomCount = roomCount;
    }

    public Integer getRoomCount() 
    {
        return roomCount;
    }

    public String getConferenceTitle() 
    {
        return conferenceTitle;
    }

    public void setConferenceTitle(String conferenceTitle) 
    {
        this.conferenceTitle = conferenceTitle;
    }

    public String getRoomTitle() 
    {
        return roomTitle;
    }

    public void setRoomTitle(String roomTitle) 
    {
        this.roomTitle = roomTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("categoryId", getCategoryId())
            .append("conference", getConference())
            .append("roomId", getRoomId())
            .append("roomCount", getRoomCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("conferenceTitle", getConferenceTitle())
            .append("roomTitle", getRoomTitle())
            .toString();
    }
}
