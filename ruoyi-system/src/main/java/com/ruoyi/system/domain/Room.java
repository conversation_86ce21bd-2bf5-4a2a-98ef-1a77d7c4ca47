package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 房型对象 room
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
public class Room extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 房型ID */
    private Long id;

    /** 会议ID */
    @Excel(name = "会议ID")
    private Long conferenceId;

    /** 房间名称 */
    @Excel(name = "房间名称")
    private String roomTitle;

    /** 房间图片 */
    @Excel(name = "房间图片")
    private String roomImgUrl;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;

    /** 会议名称（关联查询用） */
    private String conferenceTitle;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setConferenceId(Long conferenceId) 
    {
        this.conferenceId = conferenceId;
    }

    public Long getConferenceId() 
    {
        return conferenceId;
    }
    public void setRoomTitle(String roomTitle) 
    {
        this.roomTitle = roomTitle;
    }

    public String getRoomTitle() 
    {
        return roomTitle;
    }
    public void setRoomImgUrl(String roomImgUrl) 
    {
        this.roomImgUrl = roomImgUrl;
    }

    public String getRoomImgUrl() 
    {
        return roomImgUrl;
    }
    public void setOperator(String operator) 
    {
        this.operator = operator;
    }

    public String getOperator() 
    {
        return operator;
    }

    public String getConferenceTitle() 
    {
        return conferenceTitle;
    }

    public void setConferenceTitle(String conferenceTitle) 
    {
        this.conferenceTitle = conferenceTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("conferenceId", getConferenceId())
            .append("roomTitle", getRoomTitle())
            .append("roomImgUrl", getRoomImgUrl())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("operator", getOperator())
            .append("conferenceTitle", getConferenceTitle())
            .toString();
    }
}
