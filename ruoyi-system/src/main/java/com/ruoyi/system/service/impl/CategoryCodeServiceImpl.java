package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.CategoryCodeMapper;
import com.ruoyi.system.domain.CategoryCode;
import com.ruoyi.system.service.ICategoryCodeService;

/**
 * 识别码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class CategoryCodeServiceImpl implements ICategoryCodeService 
{
    @Autowired
    private CategoryCodeMapper categoryCodeMapper;

    /**
     * 查询识别码
     * 
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 识别码
     */
    @Override
    public CategoryCode selectCategoryCodeById(String categoryId, Long conference)
    {
        return categoryCodeMapper.selectCategoryCodeById(categoryId, conference);
    }

    /**
     * 查询识别码列表
     * 
     * @param categoryCode 识别码
     * @return 识别码
     */
    @Override
    public List<CategoryCode> selectCategoryCodeList(CategoryCode categoryCode)
    {
        return categoryCodeMapper.selectCategoryCodeList(categoryCode);
    }

    /**
     * 新增识别码
     * 
     * @param categoryCode 识别码
     * @return 结果
     */
    @Override
    public int insertCategoryCode(CategoryCode categoryCode)
    {
        categoryCode.setCreateTime(DateUtils.getNowDate());
        return categoryCodeMapper.insertCategoryCode(categoryCode);
    }

    /**
     * 修改识别码
     * 
     * @param categoryCode 识别码
     * @return 结果
     */
    @Override
    public int updateCategoryCode(CategoryCode categoryCode)
    {
        categoryCode.setUpdateTime(DateUtils.getNowDate());
        return categoryCodeMapper.updateCategoryCode(categoryCode);
    }

    /**
     * 批量删除识别码
     * 
     * @param categoryIds 需要删除的识别码主键
     * @return 结果
     */
    @Override
    public int deleteCategoryCodeByIds(String[] categoryIds)
    {
        return categoryCodeMapper.deleteCategoryCodeByIds(categoryIds);
    }

    /**
     * 删除识别码信息
     * 
     * @param categoryId 识别码主键
     * @param conference 会议ID
     * @return 结果
     */
    @Override
    public int deleteCategoryCodeById(String categoryId, Long conference)
    {
        return categoryCodeMapper.deleteCategoryCodeById(categoryId, conference);
    }

    /**
     * 验证识别码是否存在
     * 
     * @param categoryId 识别码
     * @param conference 会议ID
     * @return 识别码信息
     */
    @Override
    public CategoryCode validateCategoryCode(String categoryId, Long conference)
    {
        return categoryCodeMapper.validateCategoryCode(categoryId, conference);
    }
}
