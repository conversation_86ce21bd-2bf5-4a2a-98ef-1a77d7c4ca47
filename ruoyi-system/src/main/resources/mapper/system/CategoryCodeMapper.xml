<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CategoryCodeMapper">
    
    <resultMap type="CategoryCode" id="CategoryCodeResult">
        <result property="categoryId"    column="category_id"    />
        <result property="conference"    column="conference"    />
        <result property="roomId"    column="room_id"    />
        <result property="roomCount"    column="room_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="conferenceTitle"    column="conference_title"    />
        <result property="roomTitle"    column="room_title"    />
    </resultMap>

    <sql id="selectCategoryCodeVo">
        select cc.category_id, cc.conference, cc.room_id, cc.room_count, cc.create_time, cc.update_time, 
               c.conference_title, r.room_title
        from category_code cc
        left join conference c on cc.conference = c.id
        left join room r on cc.room_id = r.id
    </sql>

    <select id="selectCategoryCodeList" parameterType="CategoryCode" resultMap="CategoryCodeResult">
        <include refid="selectCategoryCodeVo"/>
        <where>  
            <if test="categoryId != null  and categoryId != ''"> and cc.category_id = #{categoryId}</if>
            <if test="conference != null "> and cc.conference = #{conference}</if>
            <if test="roomId != null "> and cc.room_id = #{roomId}</if>
            <if test="roomCount != null "> and cc.room_count = #{roomCount}</if>
            <if test="conferenceTitle != null  and conferenceTitle != ''"> and c.conference_title like concat('%', #{conferenceTitle}, '%')</if>
            <if test="roomTitle != null  and roomTitle != ''"> and r.room_title like concat('%', #{roomTitle}, '%')</if>
        </where>
        order by cc.create_time desc
    </select>
    
    <select id="selectCategoryCodeById" resultMap="CategoryCodeResult">
        <include refid="selectCategoryCodeVo"/>
        where cc.category_id = #{categoryId} and cc.conference = #{conference}
    </select>

    <select id="validateCategoryCode" resultMap="CategoryCodeResult">
        <include refid="selectCategoryCodeVo"/>
        where cc.category_id = #{categoryId} and cc.conference = #{conference}
    </select>
        
    <insert id="insertCategoryCode" parameterType="CategoryCode">
        insert into category_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null and categoryId != ''">category_id,</if>
            <if test="conference != null">conference,</if>
            <if test="roomId != null">room_id,</if>
            <if test="roomCount != null">room_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null and categoryId != ''">#{categoryId},</if>
            <if test="conference != null">#{conference},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="roomCount != null">#{roomCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCategoryCode" parameterType="CategoryCode">
        update category_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="roomCount != null">room_count = #{roomCount},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where category_id = #{categoryId} and conference = #{conference}
    </update>

    <delete id="deleteCategoryCodeById">
        delete from category_code where category_id = #{categoryId} and conference = #{conference}
    </delete>

    <delete id="deleteCategoryCodeByIds" parameterType="String">
        delete from category_code where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper>
