package com.ruoyi.web.controller.api;

import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.common.core.domain.request.wechat.JsCodeRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.MiniAppLoginResult;
import com.ruoyi.common.core.domain.WechatApiResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.wechat.WechatMiniAppUtils;
import com.ruoyi.common.utils.wechat.WechatSessionManager;
import com.ruoyi.common.utils.wechat.MiniAppTokenUtils;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.service.IMiniAppLoginService;
import com.ruoyi.system.service.IUserService;

/**
 * 微信小程序API接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/api/wechat")
public class WechatApiController {
    @Autowired
    private WechatMiniAppUtils wechatMiniAppUtils;

    @Autowired
    private IMiniAppLoginService miniAppLoginService;

    @Autowired
    private IUserService userService;

    /**
     * 获取微信小程序access_token
     */
    @GetMapping("/getAccessToken")
    public AjaxResult getAccessToken() {
        try {
            String accessToken = wechatMiniAppUtils.getAccessToken();
            if (StringUtils.isNotEmpty(accessToken)) {
                return AjaxResult.success("获取access_token成功", accessToken);
            } else {
                return AjaxResult.error("获取access_token失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("获取access_token异常：" + e.getMessage());
        }
    }

    /**
     * 强制刷新微信小程序access_token
     */
    @PostMapping("/refreshAccessToken")
    public AjaxResult refreshAccessToken() {
        try {
            String accessToken = wechatMiniAppUtils.getAccessToken(true);
            if (StringUtils.isNotEmpty(accessToken)) {
                return AjaxResult.success("刷新access_token成功", accessToken);
            } else {
                return AjaxResult.error("刷新access_token失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("刷新access_token异常：" + e.getMessage());
        }
    }

    /**
     * 微信小程序登录
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody @Validated JsCodeRequest jsCodeRequest) {
        String jsCode = jsCodeRequest.getJsCode();
        try {
            if (StringUtils.isEmpty(jsCode)) {
                return AjaxResult.error("jsCode不能为空");
            }

            MiniAppLoginResult result = miniAppLoginService.login(jsCode);
            if (result != null) {
                return AjaxResult.success("登录成功", result);
            } else {
                return AjaxResult.error("登录失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("登录异常：" + e.getMessage());
        }
    }

    /**
     * 微信小程序登录（带用户信息）
     */
    @PostMapping("/loginWithUserInfo")
    public AjaxResult loginWithUserInfo(
            @RequestParam String jsCode,
            @RequestParam(required = false) String nickName,
            @RequestParam(required = false) String avatarUrl,
            @RequestParam(required = false) String gender) {
        try {
            if (StringUtils.isEmpty(jsCode)) {
                return AjaxResult.error("jsCode不能为空");
            }

            MiniAppLoginResult result = miniAppLoginService.loginWithUserInfo(jsCode, nickName, avatarUrl, gender);
            if (result != null) {
                return AjaxResult.success("登录成功", result);
            } else {
                return AjaxResult.error("登录失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("登录异常：" + e.getMessage());
        }
    }

    /**
     * 获取微信用户信息（原始接口，保留用于调试）
     */
    @PostMapping("/getUserInfo")
    public AjaxResult getUserInfo(@RequestParam String jsCode) {
        try {
            if (StringUtils.isEmpty(jsCode)) {
                return AjaxResult.error("jsCode不能为空");
            }

            WechatApiResult result = wechatMiniAppUtils.code2Session(jsCode);
            if (result.isSuccess()) {
                return AjaxResult.success("获取用户信息成功", result);
            } else {
                return AjaxResult.error("获取用户信息失败：" + result.getErrMsg());
            }
        } catch (Exception e) {
            return AjaxResult.error("获取用户信息异常：" + e.getMessage());
        }
    }

    /**
     * 清除access_token缓存
     */
    @PostMapping("/clearCache")
    public AjaxResult clearCache() {
        try {
            wechatMiniAppUtils.clearAccessTokenCache();
            return AjaxResult.success("清除缓存成功");
        } catch (Exception e) {
            return AjaxResult.error("清除缓存异常：" + e.getMessage());
        }
    }

    /**
     * 根据openid查询用户信息
     */
    @GetMapping("/getUserByOpenid")
    public AjaxResult getUserByOpenid(@RequestParam String openid) {
        try {
            if (StringUtils.isEmpty(openid)) {
                return AjaxResult.error("openid不能为空");
            }

            User user = userService.selectUserByOpenid(openid);
            if (user != null) {
                return AjaxResult.success("查询成功", user);
            } else {
                return AjaxResult.error("用户不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("查询异常：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID查询用户信息
     */
    @GetMapping("/getUserById")
    public AjaxResult getUserById(@RequestParam Integer id) {
        try {
            if (id == null || id <= 0) {
                return AjaxResult.error("用户ID不能为空");
            }

            User user = userService.selectUserById(id);
            if (user != null) {
                return AjaxResult.success("查询成功", user);
            } else {
                return AjaxResult.error("用户不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("查询异常：" + e.getMessage());
        }
    }

    /**
     * 检查用户是否有有效的sessionKey（管理员接口）
     */
    @GetMapping("/checkSessionKey")
    public AjaxResult checkSessionKey(@RequestParam String openid)
    {
        try
        {
            if (StringUtils.isEmpty(openid))
            {
                return AjaxResult.error("openid不能为空");
            }

            boolean hasValidSessionKey = miniAppLoginService.hasValidSessionKey(openid);
            return AjaxResult.success("检查完成", hasValidSessionKey);
        }
        catch (Exception e)
        {
            return AjaxResult.error("检查异常：" + e.getMessage());
        }
    }

    /**
     * 获取SessionKey缓存统计信息（管理员接口）
     */
    @GetMapping("/getSessionStats")
    public AjaxResult getSessionStats()
    {
        try
        {
            int cacheSize = WechatSessionManager.getCacheSize();
            return AjaxResult.success("获取统计信息成功", "当前缓存的sessionKey数量：" + cacheSize);
        }
        catch (Exception e)
        {
            return AjaxResult.error("获取统计信息异常：" + e.getMessage());
        }
    }

    /**
     * 清理过期的SessionKey（管理员接口）
     */
    @PostMapping("/cleanExpiredSessions")
    public AjaxResult cleanExpiredSessions()
    {
        try
        {
            WechatSessionManager.cleanExpiredSessions();
            return AjaxResult.success("清理过期sessionKey成功");
        }
        catch (Exception e)
        {
            return AjaxResult.error("清理过期sessionKey异常：" + e.getMessage());
        }
    }

    /**
     * 验证小程序token（测试接口）
     */
    @PostMapping("/validateToken")
    public AjaxResult validateToken(@RequestParam String token) {
        try {
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error("token不能为空");
            }

            MiniAppTokenUtils.TokenValidationResult result = MiniAppTokenUtils.validateToken(token);
            if (result.isValid()) {
                return AjaxResult.success("token验证成功", result);
            } else {
                return AjaxResult.error("token验证失败：" + result.getMessage());
            }
        } catch (Exception e) {
            return AjaxResult.error("token验证异常：" + e.getMessage());
        }
    }

    /**
     * 刷新小程序token（测试接口）
     */
    @PostMapping("/refreshToken")
    public AjaxResult refreshToken(@RequestParam String token) {
        try {
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error("token不能为空");
            }

            String newToken = MiniAppTokenUtils.refreshToken(token);
            if (StringUtils.isNotEmpty(newToken)) {
                return AjaxResult.success("token刷新成功", newToken);
            } else {
                return AjaxResult.error("token刷新失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("token刷新异常：" + e.getMessage());
        }
    }
}
