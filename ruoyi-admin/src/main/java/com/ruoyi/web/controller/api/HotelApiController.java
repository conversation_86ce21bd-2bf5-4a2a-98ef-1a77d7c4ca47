package com.ruoyi.web.controller.api;

import java.util.List;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.domain.Room;
import com.ruoyi.system.domain.CategoryCode;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.system.service.IRoomService;
import com.ruoyi.system.service.ICategoryCodeService;

/**
 * 酒店预定小程序API接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/api/hotel")
public class HotelApiController {
    @Autowired
    private IConferenceService conferenceService;

    @Autowired
    private IRoomService roomService;

    @Autowired
    private ICategoryCodeService categoryCodeService;

    /**
     * 获取当前登录的小程序用户
     */
    private User getCurrentUser() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            if (principal instanceof User) {
                return (User) principal;
            }
        }
        return null;
    }

    /**
     * 获取启用的会议列表（小程序首页）
     */
    @GetMapping("/conferences")
    public AjaxResult getConferences() {
        List<Conference> conferences = conferenceService.selectEnabledConferenceList();
        return AjaxResult.success(conferences);
    }

    /**
     * 获取会议详情
     */
    @GetMapping("/conference")
    public AjaxResult getConference(@RequestParam Long id) {
        Conference conference = conferenceService.selectConferenceById(id);
        if (conference == null) {
            return AjaxResult.error("会议不存在");
        }
        if (!conference.enable()) {
            return AjaxResult.error("会议未启用");
        }
        return AjaxResult.success(conference);
    }

    /**
     * 验证会议识别码
     */
    @PostMapping("/validateCode")
    public AjaxResult validateCode(@RequestParam String categoryId, @RequestParam Long conferenceId) {
        // 获取当前登录用户（Shiro已自动验证token）
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return AjaxResult.error("用户未登录");
        }

        // 验证会议是否存在且启用
        Conference conference = conferenceService.selectConferenceById(conferenceId);
        if (conference == null || !conference.enable()) {
            return AjaxResult.error("会议不存在或未启用");
        }

        // 验证识别码
        CategoryCode categoryCode = categoryCodeService.validateCategoryCode(categoryId, conferenceId);
        if (categoryCode == null) {
            return AjaxResult.error("识别码不正确");
        }

        // 记录用户操作日志
        System.out.println("用户 " + currentUser.getNickName() + "(" + currentUser.getOpenid() + ") 验证识别码：" + categoryId);

        return AjaxResult.success("验证成功", categoryCode);
    }

    /**
     * 根据会议ID获取房型列表
     */
    @GetMapping("/rooms")
    public AjaxResult getRoomsByConference(@RequestParam Long conferenceId) {
        // 验证会议是否存在且启用
        Conference conference = conferenceService.selectConferenceById(conferenceId);
        if (conference == null || !conference.enable()) {
            return AjaxResult.error("会议不存在或未启用");
        }

        List<Room> rooms = roomService.selectRoomListByConferenceId(conferenceId);
        return AjaxResult.success(rooms);
    }

    /**
     * 获取房型详情
     */
    @GetMapping("/room")
    public AjaxResult getRoom(@RequestParam Long id) {
        Room room = roomService.selectRoomById(id);
        if (room == null) {
            return AjaxResult.error("房型不存在");
        }

        // 验证关联的会议是否启用
        Conference conference = conferenceService.selectConferenceById(room.getConferenceId());
        if (conference == null || !conference.enable()) {
            return AjaxResult.error("关联会议不存在或未启用");
        }

        return AjaxResult.success(room);
    }

    /**
     * 根据识别码获取可预订的房型信息
     */
    @PostMapping("/getRoomByCode")
    public AjaxResult getRoomByCode(@RequestParam String categoryId, @RequestParam Long conferenceId) {
        // 获取当前登录用户（Shiro已自动验证token）
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            return AjaxResult.error("用户未登录");
        }

        // 验证识别码
        CategoryCode categoryCode = categoryCodeService.validateCategoryCode(categoryId, conferenceId);
        if (categoryCode == null) {
            return AjaxResult.error("识别码不正确");
        }

        // 获取房型信息
        if (categoryCode.getRoomId() != null) {
            Room room = roomService.selectRoomById(categoryCode.getRoomId());
            if (room != null) {
                // 记录用户操作日志
                System.out.println("用户 " + currentUser.getNickName() + "(" + currentUser.getOpenid() + ") 查询房型：" + room.getRoomTitle());

                // 将房间数量信息添加到返回结果中
                AjaxResult result = AjaxResult.success(room);
                result.put("roomCount", categoryCode.getRoomCount());
                return result;
            }
        }

        return AjaxResult.error("房型信息不存在");
    }
}
