package com.ruoyi.web.controller.hotel;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Conference;
import com.ruoyi.system.service.IConferenceService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.SimpleDateFormat;

/**
 * 会议Controller
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Controller
@RequestMapping("/hotel/conference")
public class ConferenceController extends BaseController
{
    private String prefix = "hotel/conference";

    @Autowired
    private IConferenceService conferenceService;

    @RequiresPermissions("hotel:conference:view")
    @GetMapping()
    public String conference()
    {
        return prefix + "/conference";
    }

    /**
     * 查询会议列表
     */
    @RequiresPermissions("hotel:conference:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Conference conference)
    {
        startPage();
        List<Conference> list = conferenceService.selectConferenceList(conference);
        return getDataTable(list);
    }

    /**
     * 导出会议列表
     */
    @RequiresPermissions("hotel:conference:export")
    @Log(title = "会议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Conference conference)
    {
        List<Conference> list = conferenceService.selectConferenceList(conference);
        ExcelUtil<Conference> util = new ExcelUtil<Conference>(Conference.class);
        return util.exportExcel(list, "会议数据");
    }

    /**
     * 新增会议
     */
    @RequiresPermissions("hotel:conference:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存会议
     */
    @RequiresPermissions("hotel:conference:add")
    @Log(title = "会议", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Conference conference, @RequestParam(value = "file", required = false) MultipartFile file)
    {
        try
        {
            // 处理图片上传
            if (file != null && !file.isEmpty())
            {
                // 上传文件路径
                String filePath = RuoYiConfig.getUploadPath();
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file, MimeTypeUtils.IMAGE_EXTENSION);
                conference.setPhotoPath(fileName);
            }

            conference.setOperator(getLoginName());
            return toAjax(conferenceService.insertConference(conference));
        }
        catch (Exception e)
        {
            return AjaxResult.error("新增会议失败：" + e.getMessage());
        }
    }

    /**
     * 修改会议
     */
    @RequiresPermissions("hotel:conference:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        Conference conference = conferenceService.selectConferenceById(id);
        mmap.put("conference", conference);
        return prefix + "/edit";
    }

    /**
     * 修改保存会议
     */
    @RequiresPermissions("hotel:conference:edit")
    @Log(title = "会议", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Conference conference, @RequestParam(value = "file", required = false) MultipartFile file)
    {
        try
        {
            // 处理图片上传
            if (file != null && !file.isEmpty())
            {
                // 上传文件路径
                String filePath = RuoYiConfig.getUploadPath();
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file, MimeTypeUtils.IMAGE_EXTENSION);
                conference.setPhotoPath(fileName);
            }

            conference.setOperator(getLoginName());
            return toAjax(conferenceService.updateConference(conference));
        }
        catch (Exception e)
        {
            return AjaxResult.error("修改会议失败：" + e.getMessage());
        }
    }

    /**
     * 删除会议
     */
    @RequiresPermissions("hotel:conference:remove")
    @Log(title = "会议", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(conferenceService.deleteConferenceByIds(Convert.toLongArray(ids)));
    }

    /**
     * 查看会议详情
     */
    @RequiresPermissions("hotel:conference:view")
    @GetMapping("/view/{id}")
    public String view(@PathVariable("id") Long id, ModelMap mmap)
    {
        Conference conference = conferenceService.selectConferenceById(id);
        mmap.put("conference", conference);
        return prefix + "/view";
    }
}
