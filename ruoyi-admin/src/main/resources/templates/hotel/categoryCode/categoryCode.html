<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('识别码列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>识别码：</label>
                                <input type="text" name="categoryId"/>
                            </li>
                            <li>
                                <label>会议名称：</label>
                                <input type="text" name="conferenceTitle"/>
                            </li>
                            <li>
                                <label>房间名称：</label>
                                <input type="text" name="roomTitle"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="hotel:categoryCode:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="hotel:categoryCode:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="hotel:categoryCode:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="hotel:categoryCode:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('hotel:categoryCode:edit')}]];
        var removeFlag = [[${@permission.hasPermi('hotel:categoryCode:remove')}]];
        var prefix = ctx + "hotel/categoryCode";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{categoryId}/{conference}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "识别码",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'categoryId',
                    title: '识别码'
                },
                {
                    field: 'conferenceTitle',
                    title: '会议名称'
                },
                {
                    field: 'roomTitle',
                    title: '房间名称'
                },
                {
                    field: 'roomCount',
                    title: '房间数量'
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewCategoryCode(\'' + row.categoryId + '\', \'' + row.conference + '\')"><i class="fa fa-search"></i>查看</a> ');
                        if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="editCategoryCode(\'' + row.categoryId + '\', \'' + row.conference + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (removeFlag) {
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="removeCategoryCode(\'' + row.categoryId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function viewCategoryCode(categoryId, conference) {
            var url = prefix + '/view/' + categoryId + '/' + conference;
            $.modal.openTab("识别码详情", url);
        }

        function editCategoryCode(categoryId, conference) {
            var url = prefix + '/edit/' + categoryId + '/' + conference;
            $.modal.open("修改识别码", url);
        }

        function removeCategoryCode(categoryId) {
            $.modal.confirm("确定删除该识别码吗？", function() {
                $.operate.post(prefix + "/remove", { "ids": categoryId });
            });
        }
    </script>
</body>
</html>
