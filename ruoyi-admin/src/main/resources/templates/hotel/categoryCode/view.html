<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('识别码详情')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-categoryCode-view" th:object="${categoryCode}">
            <div class="form-group">    
                <label class="col-sm-3 control-label">识别码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{categoryId}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{conferenceTitle}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{roomTitle}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间数量：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{roomCount}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">创建时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(categoryCode.createTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">更新时间：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:value="${#dates.format(categoryCode.updateTime, 'yyyy-MM-dd HH:mm:ss')}" readonly>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
