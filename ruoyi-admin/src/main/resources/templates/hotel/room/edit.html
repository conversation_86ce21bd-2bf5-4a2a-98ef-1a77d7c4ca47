<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改房型')" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-room-edit" th:object="${room}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">会议：</label>
                <div class="col-sm-8">
                    <select name="conferenceId" class="form-control" required>
                        <option value="">请选择会议</option>
                        <option th:each="conference : ${conferences}" th:value="${conference.id}" th:text="${conference.conferenceTitle}" th:selected="${conference.id == room.conferenceId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">房间名称：</label>
                <div class="col-sm-8">
                    <input name="roomTitle" th:field="*{roomTitle}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间图片：</label>
                <div class="col-sm-8">
                    <input name="roomImgUrl" th:field="*{roomImgUrl}" class="form-control" type="text" placeholder="请输入图片URL">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/room";
        $("#form-room-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-room-edit').serialize());
            }
        }
    </script>
</body>
</html>
