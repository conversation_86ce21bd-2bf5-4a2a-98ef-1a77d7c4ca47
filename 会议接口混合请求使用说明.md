# 会议添加&修改接口混合请求使用说明

## 概述

会议的添加和修改接口现在支持文件上传使用表单、其他参数使用JSON的混合请求方式。这种方式可以更好地处理复杂的数据结构和文件上传。

## 接口说明

### 1. 会议添加接口

**URL:** `POST /hotel/conference/add`

**请求方式:** `multipart/form-data`

**参数说明:**
- `data` (必需): JSON字符串，包含会议的所有非文件字段
- `file` (可选): 会议照片文件

### 2. 会议修改接口

**URL:** `POST /hotel/conference/edit`

**请求方式:** `multipart/form-data`

**参数说明:**
- `data` (必需): JSON字符串，包含会议的所有非文件字段
- `file` (可选): 会议照片文件

## JSON数据格式

```json
{
    "id": 1,
    "conferenceTitle": "2025年度技术大会",
    "startTime": "2025-03-15 09:00:00",
    "endTime": "2025-03-17 18:00:00",
    "address": "北京国际会议中心",
    "bookingOpenTime": "2025-02-01 00:00:00",
    "bookingCloseTime": "2025-03-10 23:59:59",
    "participantCount": 500,
    "hotelName": "北京国际酒店",
    "hotelAddress": "北京市朝阳区建国门外大街1号",
    "bookingRangeStart": "2025-03-14",
    "bookingRangeEnd": "2025-03-18",
    "enable": "Y",
    "photoPath": "/upload/2025/01/29/conference_photo_20250129.jpg"
}
```

## 前端实现示例

### JavaScript代码示例

```javascript
function submitHandler() {
    if ($.validate.form()) {
        // 构建JSON数据对象（排除文件字段）
        var jsonData = {};
        $('#form-conference-add').find('input, select, textarea').each(function() {
            var $this = $(this);
            var name = $this.attr('name');
            var type = $this.attr('type');
            
            // 跳过文件类型的input
            if (type === 'file') {
                return;
            }
            
            // 处理radio按钮
            if (type === 'radio') {
                if ($this.is(':checked')) {
                    jsonData[name] = $this.val();
                }
            } else if (name && $this.val()) {
                jsonData[name] = $this.val();
            }
        });
        
        // 创建FormData对象
        var formData = new FormData();
        
        // 添加JSON数据
        formData.append('data', JSON.stringify(jsonData));
        
        // 添加文件（如果有的话）
        var fileInput = $('input[name="file"]')[0];
        if (fileInput && fileInput.files.length > 0) {
            formData.append('file', fileInput.files[0]);
        }
        
        $.ajax({
            url: prefix + "/add",
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            success: function(result) {
                $.operate.successTabCallback(result);
            }
        });
    }
}
```

## 后端实现说明

### Controller方法签名

```java
@PostMapping("/add")
@ResponseBody
public AjaxResult addSave(@RequestParam("data") String jsonData, 
                         @RequestParam(value = "file", required = false) MultipartFile file)
```

### JSON解析配置

```java
// 解析JSON数据为Conference对象
ObjectMapper objectMapper = new ObjectMapper();
// 配置日期格式
SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
objectMapper.setDateFormat(dateTimeFormat);
// 配置忽略未知属性
objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
Conference conference = objectMapper.readValue(jsonData, Conference.class);
```

## 优势

1. **数据结构清晰**: JSON格式可以更好地表示复杂的数据结构
2. **类型安全**: 后端可以直接将JSON反序列化为Java对象
3. **文件上传支持**: 同时支持文件上传和结构化数据
4. **扩展性好**: 易于添加新字段而不影响现有功能
5. **调试友好**: JSON数据易于查看和调试

## 注意事项

1. 日期格式必须严格按照指定格式：`yyyy-MM-dd HH:mm:ss` 或 `yyyy-MM-dd`
2. 文件上传是可选的，如果不上传文件，原有照片路径会保持不变
3. JSON中的字段名必须与Java实体类的属性名完全匹配
4. 前端需要确保JSON数据的正确性，避免类型错误

## 测试建议

1. 测试只传JSON数据不传文件的情况
2. 测试同时传JSON数据和文件的情况
3. 测试JSON格式错误的处理
4. 测试文件格式不正确的处理
5. 测试日期格式错误的处理
