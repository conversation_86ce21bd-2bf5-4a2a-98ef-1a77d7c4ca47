CREATE TABLE `category_code` (
                                 `category_id` VARCHAR(8) NOT NULL UNIQUE COMMENT '识别码',
                                 `conference` BIGINT COMMENT '会议ID',
                                 `room_id` BIGINT COMMENT '房间ID',
                                 `room_count` INTEGER COMMENT '房间数量',
                                 `create_time` DATETIME COMMENT '创建时间',
                                 `update_time` DATETIME COMMENT '更新时间',
                                 PRIMARY KEY(`category_id`, `conference`)
) COMMENT='识别码表';


CREATE TABLE `conference` (
                              `id` BIGINT NOT NULL AUTO_INCREMENT UNIQUE,
                              `conference_title` VARCHAR(64) COMMENT '会议名称',
                              `start_time` DATETIME COMMENT '开始时间',
                              `end_time` DATETIME COMMENT '结束时间',
                              `address` VARCHAR(255) COMMENT '会议地址',
                              `booking_open_time` DATETIME COMMENT '酒店房间开放预定时间',
                              `booking_close_time` DATETIME COMMENT '酒店房间关闭预定时间',
                              `participant_count` INTEGER COMMENT '参会人员数量',
                              `hotel_name` VARCHAR(32) COMMENT '酒店名称',
                              `hotel_address` VARCHAR(255) COMMENT '酒店地址',
                              `booking_range_start` DATE COMMENT '房间预定区间起始',
                              `booking_range_end` DATE COMMENT '房间预定时间终止',
                              `enable` BOOLEAN COMMENT '是否启用,启用状态下小程序可以见',
                              `create_time` DATETIME COMMENT '创建时间',
                              `update_time` DATETIME COMMENT '更新时间',
                              `operator` VARCHAR(32) COMMENT '操作人',
                              PRIMARY KEY(`id`)
) COMMENT='会议表';


CREATE TABLE `room` (
                        `id` BIGINT NOT NULL AUTO_INCREMENT UNIQUE,
                        `conference_id` BIGINT COMMENT '会议ID',
                        `room_title` VARCHAR(32) COMMENT '房间名称',
                        `room_img_url` VARCHAR(255) COMMENT '房间图片',
                        `create_time` DATETIME COMMENT '创建时间',
                        `update_time` DATETIME COMMENT '更新时间',
                        `operator` VARCHAR(255) COMMENT '操作人',
                        PRIMARY KEY(`id`)
) COMMENT='房型表';


CREATE TABLE `user` (
                        `id` INTEGER NOT NULL AUTO_INCREMENT UNIQUE,
                        `nick_name` VARCHAR(64) COMMENT '用户昵称',
                        `real_name` VARCHAR(12) COMMENT '真实姓名',
                        `phone_number` VARCHAR(18) COMMENT '手机号',
                        `gender` CHAR(1) COMMENT '用户性别',
                        `openid` VARCHAR(64) COMMENT '微信openid',
                        `unionid` VARCHAR(64) COMMENT '用户在开放平台的唯一标识符',
                        `email` VARCHAR(64) COMMENT '邮箱',
                        `company` VARCHAR(32) COMMENT '用户所在公司',
                        `position` VARCHAR(32) COMMENT '职位',
                        `id_card` VARCHAR(18) COMMENT '身份号',
                        `reg_source` VARCHAR(12) COMMENT '注册来源',
                        `create_time` DATETIME COMMENT '创建时间',
                        `update_time` DATETIME COMMENT '更新时间',
                        PRIMARY KEY(`id`)
) COMMENT='用户表';


