# 会议接口混合请求测试用例

## 测试环境准备

1. 确保数据库已执行photo_path字段添加脚本：
```sql
ALTER TABLE `conference` ADD COLUMN `photo_path` VARCHAR(255) COMMENT '会议照片路径' AFTER `operator`;
```

2. 确保项目已重新编译并部署

## 测试用例

### 测试用例1：添加会议（仅JSON数据，无文件）

**请求方式:** POST multipart/form-data

**URL:** `/hotel/conference/add`

**参数:**
- `data`: 
```json
{
    "conferenceTitle": "测试会议1",
    "startTime": "2025-03-15 09:00:00",
    "endTime": "2025-03-17 18:00:00",
    "address": "北京国际会议中心",
    "bookingOpenTime": "2025-02-01 00:00:00",
    "bookingCloseTime": "2025-03-10 23:59:59",
    "participantCount": 100,
    "hotelName": "北京国际酒店",
    "hotelAddress": "北京市朝阳区建国门外大街1号",
    "bookingRangeStart": "2025-03-14",
    "bookingRangeEnd": "2025-03-18",
    "enable": "Y"
}
```

**预期结果:** 成功创建会议，返回成功响应

### 测试用例2：添加会议（JSON数据 + 文件）

**请求方式:** POST multipart/form-data

**URL:** `/hotel/conference/add`

**参数:**
- `data`: 同测试用例1的JSON数据
- `file`: 选择一个图片文件（jpg/png/gif等）

**预期结果:** 成功创建会议，返回成功响应，数据库中photo_path字段有值

### 测试用例3：修改会议（仅JSON数据，无文件）

**请求方式:** POST multipart/form-data

**URL:** `/hotel/conference/edit`

**参数:**
- `data`: 
```json
{
    "id": 1,
    "conferenceTitle": "修改后的会议名称",
    "startTime": "2025-03-15 09:00:00",
    "endTime": "2025-03-17 18:00:00",
    "address": "上海国际会议中心",
    "bookingOpenTime": "2025-02-01 00:00:00",
    "bookingCloseTime": "2025-03-10 23:59:59",
    "participantCount": 200,
    "hotelName": "上海国际酒店",
    "hotelAddress": "上海市浦东新区世纪大道1号",
    "bookingRangeStart": "2025-03-14",
    "bookingRangeEnd": "2025-03-18",
    "enable": "Y",
    "photoPath": "/upload/2025/01/29/existing_photo.jpg"
}
```

**预期结果:** 成功修改会议，原有照片路径保持不变

### 测试用例4：修改会议（JSON数据 + 新文件）

**请求方式:** POST multipart/form-data

**URL:** `/hotel/conference/edit`

**参数:**
- `data`: 同测试用例3的JSON数据
- `file`: 选择一个新的图片文件

**预期结果:** 成功修改会议，photo_path字段更新为新上传的文件路径

## 错误测试用例

### 错误测试用例1：JSON格式错误

**参数:**
- `data`: `{"conferenceTitle": "测试会议", "startTime": "invalid-date"}`

**预期结果:** 返回错误信息，提示JSON解析失败

### 错误测试用例2：缺少必需参数

**参数:**
- `data`: `{"address": "北京"}`（缺少会议名称等必需字段）

**预期结果:** 返回错误信息或验证失败

### 错误测试用例3：文件格式不正确

**参数:**
- `data`: 正确的JSON数据
- `file`: 选择一个非图片文件（如.txt文件）

**预期结果:** 返回错误信息，提示文件格式不支持

## 使用Postman测试

### 设置步骤：

1. 创建新的POST请求
2. URL设置为：`http://localhost:8080/hotel/conference/add`
3. 在Headers中添加必要的认证信息
4. 在Body中选择form-data
5. 添加key为`data`，value为JSON字符串的参数
6. 添加key为`file`，type选择File，选择图片文件
7. 发送请求

### 验证步骤：

1. 检查响应状态码是否为200
2. 检查响应body中的success字段是否为true
3. 查询数据库确认数据是否正确插入
4. 检查文件是否正确上传到指定目录

## 前端页面测试

1. 访问会议管理页面
2. 点击"添加"按钮
3. 填写表单数据
4. 选择图片文件
5. 点击提交
6. 检查是否成功创建会议

## 注意事项

1. 测试前确保有足够的权限访问相关接口
2. 确保上传目录有写入权限
3. 注意日期格式必须严格按照指定格式
4. 测试时可以通过浏览器开发者工具查看实际发送的请求数据
