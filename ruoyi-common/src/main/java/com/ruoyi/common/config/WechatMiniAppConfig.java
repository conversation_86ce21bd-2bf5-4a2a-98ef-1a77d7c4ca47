package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信小程序配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wechat.miniapp")
public class WechatMiniAppConfig
{
    /** 小程序AppID */
    private String appId;

    /** 小程序AppSecret */
    private String appSecret;

    /** access_token缓存时间（秒），默认7200秒（2小时） */
    private int tokenCacheTime = 7200;

    public String getAppId()
    {
        return appId;
    }

    public void setAppId(String appId)
    {
        this.appId = appId;
    }

    public String getAppSecret()
    {
        return appSecret;
    }

    public void setAppSecret(String appSecret)
    {
        this.appSecret = appSecret;
    }

    public int getTokenCacheTime()
    {
        return tokenCacheTime;
    }

    public void setTokenCacheTime(int tokenCacheTime)
    {
        this.tokenCacheTime = tokenCacheTime;
    }
}
