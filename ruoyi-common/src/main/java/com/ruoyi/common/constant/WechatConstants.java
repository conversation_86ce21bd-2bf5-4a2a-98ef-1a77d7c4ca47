package com.ruoyi.common.constant;

/**
 * 微信小程序常量信息
 * 
 * <AUTHOR>
 */
public class WechatConstants
{
    /**
     * 微信小程序获取access_token的URL
     */
    public static final String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    /**
     * 微信小程序获取用户信息的URL
     */
    public static final String WECHAT_USER_INFO_URL = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    /**
     * 微信小程序发送订阅消息的URL
     */
    public static final String WECHAT_SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";

    /**
     * access_token缓存键前缀
     */
    public static final String ACCESS_TOKEN_CACHE_KEY = "wechat:access_token:";

    /**
     * 微信API成功返回码
     */
    public static final int WECHAT_SUCCESS_CODE = 0;

    /**
     * access_token过期错误码
     */
    public static final int ACCESS_TOKEN_EXPIRED_CODE = 40001;

    /**
     * access_token无效错误码
     */
    public static final int ACCESS_TOKEN_INVALID_CODE = 42001;
}
